<?php

declare(strict_types=1);

namespace App\Exceptions\Payment;

use Exception;

/**
 * Payment Exception
 *
 * Thrown when payment operations fail.
 */
class PaymentException extends Exception
{
    /**
     * Create a new PaymentException instance.
     */
    public function __construct(string $message = 'Payment operation failed', int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
