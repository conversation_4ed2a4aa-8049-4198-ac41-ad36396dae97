<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\Payment\PaymentException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Payment Service Client
 *
 * This class provides a client for interacting with the Payment Service API.
 * Based on the QuickServe service implementation with resilience patterns.
 */
class PaymentServiceClient
{
    /**
     * The base URL for the Payment Service API.
     */
    protected string $baseUrl;

    /**
     * Service authentication token.
     */
    protected ?string $serviceToken;

    /**
     * Create a new PaymentServiceClient instance.
     */
    public function __construct(
        ?string $baseUrl = null,
        /**
         * The timeout for API requests in seconds.
         */
        protected int $timeout = 30,
        /**
         * The retry attempts for API requests.
         */
        protected int $retries = 3
    ) {
        // Use localhost URLs for local development
        // $this->baseUrl = $baseUrl ?? config('services.payment.url', 'http://127.0.0.1:8002/api');
        $this->baseUrl = $baseUrl ?? 'http://payment-service:9003/api';
        $this->serviceToken = config('services.payment_service.token', env('SERVICE_INTERNAL_TOKEN'));
    }

    /**
     * Initiate a payment with the payment-service
     *
     * @param int $customerId Customer ID (required by payment service)
     * @param float $amount Payment amount (required)
     * @param string $successUrl Success redirect URL (required)
     * @param string $failureUrl Failure redirect URL (required)
     * @param string|null $customerEmail Customer email (optional)
     * @param string|null $customerPhone Customer phone (optional)
     * @param string|null $customerName Customer name (optional)
     * @param string|null $orderId Order ID (optional)
     * @param string $referer Source platform (default: website)
     * @param string $context Payment context (default: wallet_topup)
     * @param array $metadata Additional metadata
     * @return array { id, status, redirect_url?, reference }
     * @throws PaymentException
     */
    public function createPayment(
        int $customerId,
        float $amount,
        string $successUrl,
        string $failureUrl,
        ?string $customerEmail = null,
        ?string $customerPhone = null,
        ?string $customerName = null,
        ?string $orderId = null,
        string $referer = 'website',
        string $context = 'wallet_topup',
        array $metadata = []
    ): array {
        try {
            // Build payload according to payment service requirements
            $payload = [
                'customer_id' => $customerId,
                'amount' => $amount,
                'success_url' => $successUrl,
                'failure_url' => $failureUrl,
                'referer' => $referer,
                'context' => $context,
            ];

            // Add optional fields if provided
            if ($customerEmail) {
                $payload['customer_email'] = $customerEmail;
            }
            if ($customerPhone) {
                $payload['customer_phone'] = $customerPhone;
            }
            if ($customerName) {
                $payload['customer_name'] = $customerName;
            }
            if ($orderId) {
                $payload['order_id'] = $orderId;
            }

            $response = $this->http()
                ->post('/v2/payments', $payload);

            if ($response->successful()) {
                $data = $response->json('data');
                return [
                    'id' => $data['transaction_id'] ?? $data['payment_id'] ?? $data['id'] ?? null,
                    'status' => $data['status'] ?? 'pending',
                    'redirect_url' => $data['redirect_url'] ?? null,
                    'reference' => $orderId ?? uniqid('wallet_'),
                ];
            }

            Log::error('PaymentServiceClient.createPayment failed', [
                'status' => $response->status(),
                'body' => $response->json(),
                'payload' => $payload,
            ]);

            throw new PaymentException('Failed to initiate payment: ' . ($response->json('message') ?? 'Unknown error'));
        } catch (\Exception $e) {
            Log::error('Payment service initiation failed', [
                'error' => $e->getMessage(),
                'customer_id' => $customerId,
                'amount' => $amount,
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get payment status
     *
     * @throws PaymentException
     */
    public function getStatus(string $paymentId): array
    {
        try {
            $response = $this->http()
                ->get("/v2/payments/{$paymentId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException('Failed to fetch payment status: ' . ($response->json('message') ?? 'Unknown error'));
        } catch (\Exception $e) {
            Log::error('Payment service status check failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            throw new PaymentException('Failed to get payment status: ' . $e->getMessage());
        }
    }

    /**
     * Ping the payment service to check if it's available.
     */
    public function ping(): bool
    {
        try {
            $response = $this->http()
                ->get('/v2/payments/health');

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Payment service ping failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create an HTTP client instance.
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name', 'customer-service-v12'),
                'X-Service-Version' => config('app.version', '1.0.0'),
                'X-Correlation-ID' => uniqid('cust_'),
            ])
            ->when($this->serviceToken, function ($http) {
                return $http->withToken($this->serviceToken);
            });
    }
}
