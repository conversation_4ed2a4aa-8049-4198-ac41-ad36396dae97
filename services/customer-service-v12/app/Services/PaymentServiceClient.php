<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class PaymentServiceClient
{
    protected string $baseUrl;
    protected ?string $serviceToken;

    public function __construct()
    {
        // $this->baseUrl = rtrim(config('services.payment_service.url', env('PAYMENT_SERVICE_URL', 'http://payment-service:9003/api/v1/payments')), '/');
        $this->baseUrl = rtrim('http://payment-service:9003/api/v1/payments', '/');
        $this->serviceToken = config('services.payment_service.token', env('SERVICE_INTERNAL_TOKEN')) ?? 1;
    }

    /**
     * Initiate a payment with the payment-service
     *
     * @param float $amount
     * @param string $currency
     * @param string $reference
     * @param array $metadata
     * @param string|null $returnUrl
     * @param string|null $callbackUrl Optional unified callback URL
     * @return array { id, status, redirect_url?, reference }
     */
    public function createPayment(float $amount, string $currency, string $reference, array $metadata = [], ?string $returnUrl = null, ?string $callbackUrl = null): array
    {
        $payload = [
            'amount' => $amount,
            'currency' => $currency,
            'reference' => $reference,
            'metadata' => $metadata,
        ];
        if ($returnUrl) {
            $payload['return_url'] = $returnUrl;
        }
        if ($callbackUrl) {
            $payload['callback_url'] = $callbackUrl;
        }

        $req = Http::timeout(10);
        if ($this->serviceToken) {
            $req = $req->withToken($this->serviceToken);
        }

        $resp = $req->post($this->baseUrl, $payload);
        if (!$resp->successful()) {
            Log::error('PaymentServiceClient.createPayment failed', [
                'status' => $resp->status(),
                'body' => $resp->json(),
            ]);
            throw new RuntimeException('Failed to initiate payment');
        }
        return $resp->json();
    }

    /**
     * Get payment status
     */
    public function getStatus(string $paymentId): array
    {
        $req = Http::timeout(10);
        if ($this->serviceToken) {
            $req = $req->withToken($this->serviceToken);
        }
        $resp = $req->get($this->baseUrl . '/' . urlencode($paymentId));
        if (!$resp->successful()) {
            throw new RuntimeException('Failed to fetch payment status');
        }
        return $resp->json();
    }
}
