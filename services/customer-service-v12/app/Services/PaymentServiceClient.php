<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\Payment\PaymentException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Payment Service Client
 *
 * This class provides a client for interacting with the Payment Service API.
 * Based on the QuickServe service implementation with resilience patterns.
 */
class PaymentServiceClient
{
    /**
     * The base URL for the Payment Service API.
     */
    protected string $baseUrl;

    /**
     * Service authentication token.
     */
    protected ?string $serviceToken;

    /**
     * Create a new PaymentServiceClient instance.
     */
    public function __construct(
        ?string $baseUrl = null,
        /**
         * The timeout for API requests in seconds.
         */
        protected int $timeout = 30,
        /**
         * The retry attempts for API requests.
         */
        protected int $retries = 3
    ) {
        // Use the user preference for direct service URLs
        $this->baseUrl = $baseUrl ?? config('services.payment.url', 'http://192.168.1.161:8002/api');
        $this->serviceToken = config('services.payment_service.token', env('SERVICE_INTERNAL_TOKEN'));
    }

    /**
     * Initiate a payment with the payment-service
     *
     * @param float $amount
     * @param string $currency
     * @param string $reference
     * @param array $metadata
     * @param string|null $returnUrl
     * @param string|null $callbackUrl Optional unified callback URL
     * @return array { id, status, redirect_url?, reference }
     * @throws PaymentException
     */
    public function createPayment(float $amount, string $currency, string $reference, array $metadata = [], ?string $returnUrl = null, ?string $callbackUrl = null): array
    {
        try {
            $payload = [
                'amount' => $amount,
                'currency' => $currency,
                'reference' => $reference,
                'metadata' => $metadata,
            ];

            if ($returnUrl) {
                $payload['return_url'] = $returnUrl;
            }
            if ($callbackUrl) {
                $payload['callback_url'] = $callbackUrl;
            }

            $response = $this->http()
                ->post('/v2/payments', $payload);

            if ($response->successful()) {
                $data = $response->json('data');
                return [
                    'id' => $data['payment_id'] ?? $data['id'] ?? null,
                    'status' => $data['status'] ?? 'pending',
                    'redirect_url' => $data['redirect_url'] ?? null,
                    'reference' => $reference,
                ];
            }

            Log::error('PaymentServiceClient.createPayment failed', [
                'status' => $response->status(),
                'body' => $response->json(),
                'payload' => $payload,
            ]);

            throw new PaymentException('Failed to initiate payment: ' . ($response->json('message') ?? 'Unknown error'));
        } catch (\Exception $e) {
            Log::error('Payment service initiation failed', [
                'error' => $e->getMessage(),
                'amount' => $amount,
                'reference' => $reference,
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get payment status
     *
     * @throws PaymentException
     */
    public function getStatus(string $paymentId): array
    {
        try {
            $response = $this->http()
                ->get("/v2/payments/{$paymentId}");

            if ($response->successful()) {
                return $response->json('data');
            }

            throw new PaymentException('Failed to fetch payment status: ' . ($response->json('message') ?? 'Unknown error'));
        } catch (\Exception $e) {
            Log::error('Payment service status check failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            throw new PaymentException('Failed to get payment status: ' . $e->getMessage());
        }
    }

    /**
     * Ping the payment service to check if it's available.
     */
    public function ping(): bool
    {
        try {
            $response = $this->http()
                ->get('/v2/payments/health');

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Payment service ping failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create an HTTP client instance.
     */
    protected function http(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl)
            ->timeout($this->timeout)
            ->retry($this->retries, 100)
            ->acceptJson()
            ->withHeaders([
                'X-Service' => config('app.name', 'customer-service-v12'),
                'X-Service-Version' => config('app.version', '1.0.0'),
                'X-Correlation-ID' => uniqid('cust_'),
            ])
            ->when($this->serviceToken, function ($http) {
                return $http->withToken($this->serviceToken);
            });
    }
}
