<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\WalletService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WalletPaymentController extends Controller
{
    public function __construct(private WalletService $walletService)
    {
    }

    /**
     * Unified callback from payment-service
     * Expected payload: { status: 'success'|'failure', reference: string, payment_id: string, metadata: { customer_id, company_id, ... } }
     */
    public function callback(Request $request): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:success,failure',
            'reference' => 'required|string',
            'payment_id' => 'required|string',
            'metadata.customer_id' => 'required|integer',
            'metadata.company_id' => 'required|integer',
        ]);

        $status = $request->input('status');
        $reference = $request->input('reference');
        $customerId = (int) data_get($request->input('metadata'), 'customer_id');
        // company_id is validated above and carried in metadata, used for audit/scoping by upstream services

        try {
            // Optionally verify S2S token via middleware (auth.token)
            if ($status === 'success') {
                $wallet = $this->walletService->settleTopupSuccess($customerId, $reference, $request->input('gateway_txn_id'));
                return response()->json([
                    'success' => true,
                    'message' => 'Wallet top-up credited successfully',
                    'data' => $wallet
                ]);
            }

            // failure
            $this->walletService->settleTopupFailure($customerId, $reference);
            return response()->json([
                'success' => true,
                'message' => 'Wallet top-up failed and lock removed'
            ]);
        } catch (\Throwable $e) {
            Log::error('WalletPaymentController.callback error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to process wallet payment callback'
            ], 500);
        }
    }
}
