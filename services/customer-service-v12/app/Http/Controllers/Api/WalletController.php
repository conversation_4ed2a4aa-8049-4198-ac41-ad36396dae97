<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Wallet\DepositRequest;
use App\Http\Requests\Wallet\WithdrawRequest;
use App\Services\WalletService;
use App\Services\PaymentServiceClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Wallet API Controller
 *
 * This controller handles all wallet-related API endpoints.
 */
class WalletController extends Controller
{
    /**
     * Create a new WalletController instance.
     */
    public function __construct(
        protected WalletService $walletService,
        protected PaymentServiceClient $paymentClient
    ) {
    }

    /**
     * Get a customer's wallet
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            // Validate company scope
            $request->validate([
                'company_id' => ['required', 'integer', 'min:1', 'max:99999']
            ]);

            $wallet = $this->walletService->getWallet($id);

            return response()->json([
                'success' => true,
                'data' => $wallet
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the wallet'
            ], 500);
        }
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param DepositRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function deposit(DepositRequest $request, int $id): JsonResponse
    {
        try {
            // Require company scope for deposit initiation as well
            $request->validate([
                'company_id' => ['required', 'integer', 'min:1', 'max:99999']
            ]);

            $validated = $request->validated();
            $amount = (float) $validated['amount'];
            $description = $validated['description'] ?? '';
            $companyId = (int) $request->get('company_id');

            // 1) Create a lock entry to reflect pending top-up (optional but avoids race conditions)
            // We use a reference/transaction id to tie records to the payment id later
            $reference = 'WALLET_TOPUP:' . $companyId . ':' . $id . ':' . bin2hex(random_bytes(6));

            // 2) Initiate payment with payment-service
            // $callbackBase = rtrim(config('app.url', env('APP_URL', 'http://customer-service:9001')), '/');
            $callbackBase = rtrim('http://customer-service:9001', '/');
            $callbackUrl = $callbackBase . '/api/v2/wallet/payments/callback';
            $returnUrl = $callbackBase . '/wallet/topup/return';

            $payment = $this->paymentClient->createPayment(
                $amount,
                'INR',
                $reference,
                [
                    'customer_id' => $id,
                    'company_id' => $companyId,
                    'purpose' => 'wallet_topup',
                    'source' => 'customer-service'
                ],
                $returnUrl,
                $callbackUrl
            );

            $paymentId = $payment['id'] ?? ($payment['data']['id'] ?? null);
            if (!$paymentId) {
                throw new \RuntimeException('Payment initiation did not return an id');
            }

            // 3) Record lock against the payment reference
            $this->walletService->createPendingLock($id, $amount, $reference, $description);

            return response()->json([
                'success' => true,
                'message' => 'Payment initiated. Complete payment to credit wallet.',
                'data' => [
                    'payment_id' => $paymentId,
                    'status' => $payment['status'] ?? 'pending',
                    'reference' => $reference,
                    'redirect_url' => $payment['redirect_url'] ?? null,
                    'amount' => $amount,
                    'currency' => 'INR'
                ]
            ], 202);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error depositing to wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while initiating the top-up'
            ], 500);
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param WithdrawRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function withdraw(WithdrawRequest $request, int $id): JsonResponse
    {
        try {
            $amount = $request->validated()['amount'];
            $description = $request->validated()['description'] ?? '';
            $transactionId = $request->validated()['transaction_id'] ?? '';

            $wallet = $this->walletService->withdraw($id, $amount, $description, $transactionId);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal successful',
                'data' => $wallet
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (InsufficientBalanceException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Error withdrawing from wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the withdrawal'
            ], 500);
        }
    }

    /**
     * Get wallet transaction history
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function transactions(Request $request, int $id): JsonResponse
    {
        try {
            // Validate company scope
            $request->validate([
                'company_id' => ['required', 'integer', 'min:1', 'max:99999']
            ]);

            $filters = $request->only(['type', 'status', 'date_from', 'date_to']);
            $perPage = $request->input('per_page', 15);

            // Fetch transaction history
            $transactions = $this->walletService->getTransactionHistory($id, $filters, $perPage);

            // Fetch wallet meta (balances)
            $wallet = $this->walletService->getWallet($id);

            // Shape response: include meta + paginated data
            return response()->json([
                'success' => true,
                'data' => [
                    'meta' => [
                        'customer_id' => $id,
                        'total_balance' => $wallet['balance'] ?? 0,
                        'available_balance' => $wallet['wallet_details']['available_balance'] ?? 0,
                        'usable_balance' => $wallet['wallet_details']['usable_balance'] ?? 0,
                        'locked_balance' => $wallet['wallet_details']['locked_balance'] ?? 0,
                        'currency' => $wallet['currency'] ?? 'INR',
                    ],
                    'history' => [
                        'data' => $transactions->items(),
                        'pagination' => [
                            'current_page' => $transactions->currentPage(),
                            'per_page' => $transactions->perPage(),
                            'total' => $transactions->total(),
                            'last_page' => $transactions->lastPage(),
                        ],
                    ],
                ],
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet transactions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the transactions'
            ], 500);
        }
    }

    /**
     * Get wallet balance
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getBalance(Request $request, int $id): JsonResponse
    {
        try {
            // Validate company scope
            $request->validate([
                'company_id' => ['required', 'integer', 'min:1', 'max:99999']
            ]);

            $balance = $this->walletService->getBalance($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'customer_id' => $id,
                    'balance' => $balance,
                    'currency' => 'INR'
                ]
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet balance', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the balance'
            ], 500);
        }
    }

    /**
     * Get wallet history (alias for transactions)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getHistory(Request $request, int $id): JsonResponse
    {
        return $this->transactions($request, $id);
    }

    /**
     * Get wallet details with transaction history (UI format)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getWalletWithHistory(Request $request, int $id): JsonResponse
    {
        try {
            // Validate company scope
            $request->validate([
                'company_id' => ['required', 'integer', 'min:1', 'max:99999']
            ]);

            $filters = $request->only(['type', 'date_from', 'date_to']);
            $perPage = $request->input('per_page', 15);

            $walletData = $this->walletService->getWalletWithHistory($id, $filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => $walletData
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet with history', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving wallet data'
            ], 500);
        }
    }

    /**
     * Transfer funds between wallets (placeholder)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function transfer(Request $request, int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet transfer functionality not implemented yet'
        ], 501);
    }

    /**
     * Freeze wallet (placeholder)
     *
     * @param int $id
     * @return JsonResponse
     */
    public function freeze(int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet freeze functionality not implemented yet'
        ], 501);
    }

    /**
     * Unfreeze wallet (placeholder)
     *
     * @param int $id
     * @return JsonResponse
     */
    public function unfreeze(int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet unfreeze functionality not implemented yet'
        ], 501);
    }
}
